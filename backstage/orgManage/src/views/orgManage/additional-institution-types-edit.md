# 机构额外信息编辑功能补充

## 概述
基于 Detail.vue 页面的机构类型字段，为 info.vue 页面补充了以下机构类型的编辑功能：
- 职业病诊断机构 (212)
- 职业病鉴定机构 (220)
- 职业病康复机构 (213)
- 放射卫生技术服务机构 (240)

## 新增功能详情

### 1. 职业病诊断机构 (212)

#### 字段列表
- **备案编号** (`recordNumber`): 文本输入框
- **备案日期** (`recordDate`): 日期选择器
- **备案单位名称** (`recordOrgName`): 文本输入框
- **职业病诊断类别及病种** (`diagnosisCategories`): 级联选择器（多选）
- **可以开展诊断的辖区** (`diagnosisAreas`): 地区级联选择器（多选）

#### 数据源
- 诊断类别：`getDictCascadeOptions({ key: 'occupational_disease_categories' })`
- 诊断辖区：地区字典数据（懒加载）

### 2. 职业病鉴定机构 (220)

#### 字段列表
- **鉴定机构类别** (`identificationLevel`): 下拉选择器
- **鉴定费** (`identificationFee`): 数字输入框（带"元"后缀）
- **收款人全称** (`payeeName`): 文本输入框
- **账号** (`payeeAccount`): 文本输入框
- **开户行** (`payeeBank`): 文本输入框
- **工作开始时间** (`workingHours.start`): 时间选择器
- **工作结束时间** (`workingHours.end`): 时间选择器
- **工作日** (`workingHours.workDays`): 复选框组

#### 数据源
- 鉴定机构类别：`getDictItemsByKey({ key: 'identification_level' })`

### 3. 职业病康复机构 (213)

#### 字段列表
- **机构类别** (`rehabilitationType`): 下拉选择器
- **服务辖区** (`serviceAreas`): 地区级联选择器（多选）

#### 数据源
- 机构类别：`getDictItemsByKey({ key: 'rehabilitation_type' })`
- 服务辖区：地区字典数据（懒加载）

### 4. 放射卫生技术服务机构 (240)

#### 字段列表
- **资质证书编号** (`radiationQualification`): 文本输入框
- **发证机关** (`licenseAuthority`): 文本输入框
- **发证日期** (`licenseDate`): 日期选择器
- **有效期至** (`validDate`): 日期选择器
- **放射诊疗建设项目评价资质** (`evaluationQualification`): 下拉选择器
- **检测资质** (`testingQualifications`): 多选下拉框

#### 数据源
- 评价资质：`getDictItemsByKey({ key: 'evaluation_qualification' })`
- 检测资质：`getDictItemsByKey({ key: 'testing_qualification' })`

## 技术实现

### 1. 数据结构初始化
在 `initializeFormFields` 方法中为每个机构类型添加了字段初始化逻辑：

```javascript
// 职业病诊断机构（212）
if (orgType === '212') {
  const diagnosisFields = ['recordNumber', 'recordDate', 'recordOrgName'];
  diagnosisFields.forEach(field => {
    if (this.editForm.extensions['212'][field] === undefined) {
      this.$set(this.editForm.extensions['212'], field, '');
    }
  });
  
  // 级联选择器字段初始化为数组
  if (!this.editForm.extensions['212'].diagnosisCategories || !Array.isArray(this.editForm.extensions['212'].diagnosisCategories)) {
    this.$set(this.editForm.extensions['212'], 'diagnosisCategories', []);
  }
  if (!this.editForm.extensions['212'].diagnosisAreas || !Array.isArray(this.editForm.extensions['212'].diagnosisAreas)) {
    this.$set(this.editForm.extensions['212'], 'diagnosisAreas', []);
  }
}
```

### 2. 字典数据加载
在 `initOptions` 方法中添加了新的字典数据加载：

```javascript
const [levelRes, typeRes, managementRes, classRes, gradeRes, industryRes, healthCheckRes, technicalServiceRes, diagnosisRes, identificationRes, rehabilitationRes, evaluationRes, testingRes] = await Promise.all([
  // ... 其他API调用
  getDictCascadeOptions({ key: 'occupational_disease_categories' }),
  getDictItemsByKey({ key: 'identification_level' }),
  getDictItemsByKey({ key: 'rehabilitation_type' }),
  getDictItemsByKey({ key: 'evaluation_qualification' }),
  getDictItemsByKey({ key: 'testing_qualification' }),
]);
```

### 3. 事件处理方法
添加了相应的变化处理方法：
- `onDiagnosisCategoriesChange`: 处理诊断类别变化
- `onDiagnosisAreasChange`: 处理诊断辖区变化
- `onServiceAreasChange`: 处理服务辖区变化

### 4. 编辑模式数据验证
在 `startEdit` 方法中添加了数据格式验证和级联选择器强制更新逻辑。

## 测试步骤

### 测试职业病诊断机构 (212)
1. 打开机构类型为212的机构详情页面
2. 点击"编辑"按钮进入编辑模式
3. 验证所有字段都可以正常编辑：
   - 备案编号输入框
   - 备案日期选择器
   - 备案单位名称输入框
   - 职业病诊断类别级联选择器（多选）
   - 诊断辖区级联选择器（多选）
4. 测试保存功能

### 测试职业病鉴定机构 (220)
1. 打开机构类型为220的机构详情页面
2. 点击"编辑"按钮进入编辑模式
3. 验证所有字段都可以正常编辑：
   - 鉴定机构类别下拉选择
   - 鉴定费数字输入（带"元"后缀）
   - 收款人全称、账号、开户行输入框
   - 工作时间选择器
   - 工作日复选框组
4. 测试保存功能

### 测试职业病康复机构 (213)
1. 打开机构类型为213的机构详情页面
2. 点击"编辑"按钮进入编辑模式
3. 验证所有字段都可以正常编辑：
   - 机构类别下拉选择
   - 服务辖区级联选择器（多选）
4. 测试保存功能

### 测试放射卫生技术服务机构 (240)
1. 打开机构类型为240的机构详情页面
2. 点击"编辑"按钮进入编辑模式
3. 验证所有字段都可以正常编辑：
   - 资质证书编号、发证机关输入框
   - 发证日期、有效期至日期选择器
   - 评价资质下拉选择
   - 检测资质多选下拉框
4. 测试保存功能

## 注意事项

1. **级联选择器配置**：所有级联选择器都配置了 `multiple: true` 和 `emitPath: true` 属性
2. **数据格式验证**：在编辑模式启动时会验证数组字段的格式
3. **强制更新**：使用 `$forceUpdate()` 确保级联选择器正确渲染
4. **懒加载**：地区数据使用懒加载机制提高性能
5. **响应式更新**：使用 `$set` 方法确保 Vue 响应式系统正常工作

## 相关文件

- `backstage/orgManage/src/views/orgManage/info.vue` - 主要实现文件
- `backstage/orgManage/src/views/orgManage/Detail.vue` - 参考的显示模板
- `@/api/dict` - 字典数据API
- `@/api/organization` - 机构数据API
