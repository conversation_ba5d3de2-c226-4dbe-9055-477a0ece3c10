# 级联选择器回显问题修复说明

## 问题描述
级联选择器在编辑模式下无法正确回显已选择的值，但使用固定数据时可以正常工作。

## 问题原因分析
1. **数据结构不匹配**：API返回的数据结构与el-cascader期望的格式不完全一致
2. **数据加载时机问题**：选项数据可能在组件渲染时还未完全加载
3. **绑定值格式问题**：checkCategories字段的数据格式可能不正确

## 修复方案

### 1. 添加数据格式化方法
```javascript
formatCascadeOptions(data) {
  if (!data || !Array.isArray(data)) {
    return [];
  }
  
  return data.map(item => {
    const option = {
      value: item.value,
      label: item.label || item.name,
    };
    
    // 如果有子项，递归处理
    if (item.children && Array.isArray(item.children) && item.children.length > 0) {
      option.children = this.formatCascadeOptions(item.children);
    }
    
    return option;
  });
}
```

### 2. 添加数据验证和修复方法
```javascript
validateAndFixCheckCategories() {
  if (!this.editForm.extensions || !this.editForm.extensions['211']) {
    return;
  }
  
  const categories = this.editForm.extensions['211'].checkCategories;
  
  // 如果不存在或不是数组，初始化为空数组
  if (!categories || !Array.isArray(categories)) {
    this.$set(this.editForm.extensions['211'], 'checkCategories', []);
    return;
  }
  
  // 验证数组中的每个元素都是有效的路径数组
  const validCategories = categories.filter(category => {
    return Array.isArray(category) && category.length > 0;
  });
  
  // 如果有无效数据，更新为有效数据
  if (validCategories.length !== categories.length) {
    this.$set(this.editForm.extensions['211'], 'checkCategories', validCategories);
    console.log('修复了无效的checkCategories数据:', validCategories);
  }
}
```

### 3. 添加Watch监听器
```javascript
watch: {
  // 监听职业健康检查类别选项数据变化
  occupationalHealthCategoryOptions: {
    handler(newVal) {
      console.log('职业健康检查类别选项数据变化:', newVal);
      // 当选项数据加载完成后，强制更新级联选择器
      if (newVal && newVal.length > 0) {
        this.$nextTick(() => {
          if (this.$refs.checkCategoriesCascader) {
            this.$refs.checkCategoriesCascader.$forceUpdate();
          }
        });
      }
    },
    deep: true,
    immediate: true
  }
}
```

### 4. 优化级联选择器配置
```html
<el-cascader
  v-model="editForm.extensions['211'].checkCategories"
  :options="occupationalHealthCategoryOptions"
  :props="{
    multiple: true,
    emitPath: true,
    checkStrictly: false,
    lazy: false
  }"
  placeholder="请选择职业健康检查类别及项目"
  size="small"
  style="width: 100%"
  clearable
  collapse-tags
  show-all-levels
  separator=" / "
  @change="onCheckCategoriesChange"
  ref="checkCategoriesCascader">
</el-cascader>
```

## 测试步骤
1. 打开机构管理页面
2. 选择一个职业健康检查机构进行编辑
3. 查看职业健康检查类别级联选择器是否正确回显已选择的值
4. 尝试修改选择并保存，验证数据是否正确保存

## 调试信息
修复后会在控制台输出以下调试信息：
- 职业健康检查类别选项数据变化
- 检查类别绑定值变化
- 数据格式修复信息

通过这些信息可以确认数据是否正确加载和绑定。
